<?php

namespace Tests\Unit;

use App\Filament\Imports\LinkImporter;
use App\Models\Domain;
use App\Models\Link;
use App\Models\Tag;
use App\Models\User;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms\Components\Checkbox;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class LinkImporterTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Domain $domain;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        Permission::create(['name' => 'create link']);
        Permission::create(['name' => 'update link']);
        Permission::create(['name' => 'create tag']);

        // Create a test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);

        // Create a test domain
        $this->domain = Domain::factory()->create([
            'host' => 'test.com',
            'is_active' => true,
            'is_admin_panel_active' => true,
        ]);
    }

    /**
     * Create a testable importer subclass that exposes protected properties
     */
    private function createTestableImporter(): TestableLinkImporter
    {
        // Create a mock Import model
        $import = new Import();
        $import->id = 1;
        $import->user_id = $this->user->id;
        $import->file_name = 'test.csv';
        $import->file_path = 'test/path.csv';
        $import->importer = LinkImporter::class;
        $import->total_rows = 10;
        $import->save();

        return new TestableLinkImporter($import, [], []);
    }

    #[Test]
    public function it_returns_correct_number_of_columns(): void
    {
        $columns = LinkImporter::getColumns();
        $this->assertIsArray($columns);
        $this->assertCount(12, $columns);
    }

    #[Test]
    public function it_has_required_columns(): void
    {
        $columns = LinkImporter::getColumns();
        $columnNames = array_map(fn($column) => $column->getName(), $columns);

        $expectedColumns = [
            'id', 'original_url', 'slug', 'password', 'is_active',
            'available_at', 'unavailable_at', 'forward_query_parameters',
            'send_ref_query_parameter', 'domains', 'tags', 'description'
        ];

        foreach ($expectedColumns as $expectedColumn) {
            $this->assertContains($expectedColumn, $columnNames);
        }
    }

    #[Test]
    public function it_resolves_new_record_when_id_is_null(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => null, 'original_url' => 'https://example.com'];

        $record = $importer->resolveRecord();

        $this->assertInstanceOf(Link::class, $record);
        $this->assertFalse($record->exists);
    }

    #[Test]
    public function it_resolves_existing_record_when_id_is_provided(): void
    {
        $link = Link::factory()->create();
        $link->domains()->attach($this->domain);

        $importer = $this->createMockImporter();
        $importer->data = ['id' => $link->id, 'original_url' => $link->original_url];

        $record = $importer->resolveRecord();

        $this->assertInstanceOf(Link::class, $record);
        $this->assertTrue($record->exists);
        $this->assertEquals($link->id, $record->id);
    }

    #[Test]
    public function it_removes_slug_when_updating_existing_link(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => 1, 'slug' => 'test-slug', 'original_url' => 'https://example.com'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('slug', $importer->data);
        $this->assertEquals(1, $importer->data['id']);
    }

    #[Test]
    public function it_removes_empty_id_when_creating_new_link(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => '', 'original_url' => 'https://example.com'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('id', $importer->data);
    }

    #[Test]
    public function it_sets_default_values_for_boolean_fields(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['original_url' => 'https://example.com'];

        $importer->beforeFill();

        $this->assertTrue($importer->data['is_active']);
        $this->assertFalse($importer->data['forward_query_parameters']);
        $this->assertFalse($importer->data['send_ref_query_parameter']);
    }

    #[Test]
    public function it_preserves_existing_boolean_values(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = [
            'original_url' => 'https://example.com',
            'is_active' => false,
            'forward_query_parameters' => true,
            'send_ref_query_parameter' => true,
        ];

        $importer->beforeFill();

        $this->assertFalse($importer->data['is_active']);
        $this->assertTrue($importer->data['forward_query_parameters']);
        $this->assertTrue($importer->data['send_ref_query_parameter']);
    }

    #[Test]
    public function it_generates_correct_notification_body_with_failures(): void
    {
        // Create a real Import model for testing
        $import = new Import();
        $import->successful_rows = 15;
        $import->failed_rows = 3;

        $body = LinkImporter::getCompletedNotificationBody($import);

        $this->assertEquals('Your link import has completed and 15 rows imported. 3 rows failed to import.', $body);
    }

    #[Test]
    public function it_generates_correct_notification_body_without_failures(): void
    {
        // Create a real Import model for testing
        $import = new Import();
        $import->successful_rows = 10;
        $import->failed_rows = 0;

        $body = LinkImporter::getCompletedNotificationBody($import);

        $this->assertEquals('Your link import has completed and 10 rows imported.', $body);
    }

    #[Test]
    public function it_shows_create_missing_tags_option_when_user_has_permission(): void
    {
        $this->user->givePermissionTo('create tag');

        $components = LinkImporter::getOptionsFormComponents();

        $this->assertCount(1, $components);
        $this->assertInstanceOf(Checkbox::class, $components[0]);
        $this->assertTrue($components[0]->isVisible());
    }

    #[Test]
    public function it_hides_create_missing_tags_option_when_user_lacks_permission(): void
    {
        // User doesn't have 'create tag' permission

        $components = LinkImporter::getOptionsFormComponents();

        $this->assertCount(1, $components);
        $this->assertInstanceOf(Checkbox::class, $components[0]);
        $this->assertFalse($components[0]->isVisible());
    }

    #[Test]
    public function it_validates_boolean_columns_configuration(): void
    {
        $columns = LinkImporter::getColumns();
        $booleanColumns = ['is_active', 'forward_query_parameters', 'send_ref_query_parameter'];

        foreach ($booleanColumns as $columnName) {
            $column = collect($columns)->first(fn($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(ImportColumn::class, $column);
        }
    }

    #[Test]
    public function it_validates_date_columns_configuration(): void
    {
        $columns = LinkImporter::getColumns();
        $dateColumns = ['available_at', 'unavailable_at'];

        foreach ($dateColumns as $columnName) {
            $column = collect($columns)->first(fn($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(ImportColumn::class, $column);
        }
    }

    #[Test]
    public function it_handles_null_id_in_data(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => null, 'original_url' => 'https://example.com'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('id', $importer->data);
    }

    #[Test]
    public function it_handles_zero_id_in_data(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => 0, 'original_url' => 'https://example.com'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('id', $importer->data);
    }

    #[Test]
    public function it_preserves_valid_id_in_data(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => 123, 'original_url' => 'https://example.com'];

        $importer->beforeFill();

        $this->assertEquals(123, $importer->data['id']);
    }

    #[Test]
    public function it_handles_relationship_columns(): void
    {
        $columns = LinkImporter::getColumns();
        $relationshipColumns = ['domains', 'tags'];

        foreach ($relationshipColumns as $columnName) {
            $column = collect($columns)->first(fn($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(\App\Filament\Actions\Imports\SyncRelationshipImportColumn::class, $column);
        }
    }

    #[Test]
    public function it_creates_missing_tags_when_option_enabled_and_user_has_permission(): void
    {
        $this->user->givePermissionTo('create tag');

        // Create some existing tags
        $existingTag = Tag::factory()->create(['name' => 'existing-tag']);

        // Test the tag creation logic directly
        $options = ['create_missing_tags' => true];
        $state = ['existing-tag', 'new-tag-1', 'new-tag-2'];

        // Simulate the logic from the resolver
        if (($options['create_missing_tags'] ?? false) && auth()->user()->can('create tag')) {
            $existingTags = Tag::whereIn('name', $state)->pluck('name');
            $missingTags = collect($state)->diff($existingTags);

            if ($missingTags->isNotEmpty()) {
                $missingTags->each(fn ($name) => Tag::create(['name' => $name]));
            }
        }

        // Verify new tags were created
        $this->assertDatabaseHas('tags', ['name' => 'new-tag-1']);
        $this->assertDatabaseHas('tags', ['name' => 'new-tag-2']);
        $this->assertDatabaseHas('tags', ['name' => 'existing-tag']);
    }

    #[Test]
    public function it_does_not_create_missing_tags_when_user_lacks_permission(): void
    {
        // User doesn't have 'create tag' permission
        $initialTagCount = Tag::count();

        // Test the tag creation logic directly
        $options = ['create_missing_tags' => true];
        $state = ['non-existing-tag'];

        // Simulate the logic from the resolver
        if (($options['create_missing_tags'] ?? false) && auth()->user()->can('create tag')) {
            $existingTags = Tag::whereIn('name', $state)->pluck('name');
            $missingTags = collect($state)->diff($existingTags);

            if ($missingTags->isNotEmpty()) {
                $missingTags->each(fn ($name) => Tag::create(['name' => $name]));
            }
        }

        // Should not create any new tags
        $this->assertEquals($initialTagCount, Tag::count());
        $this->assertDatabaseMissing('tags', ['name' => 'non-existing-tag']);
    }
}

/**
 * Testable subclass that exposes protected properties for testing
 */
class TestableLinkImporter extends LinkImporter
{
    public array $data = [];

    public function setData(array $data): void
    {
        $this->data = $data;
    }

    public function getData(): array
    {
        return $this->data;
    }
}
