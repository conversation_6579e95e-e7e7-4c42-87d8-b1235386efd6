<?php

namespace Tests\Unit;

use App\Filament\Imports\UserImporter;
use App\Models\User;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserImporterTest extends TestCase
{
    use RefreshDatabase;

    protected User $authUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        Permission::create(['name' => 'create user']);
        Permission::create(['name' => 'update user']);

        // Create a test user for authentication
        $this->authUser = User::factory()->create();
        $this->actingAs($this->authUser);
    }

    /**
     * Create a mock importer instance for testing
     */
    private function createMockImporter(): UserImporter
    {
        // Create a mock Import model
        $import = new Import();
        $import->id = 1;
        $import->user_id = $this->authUser->id;
        $import->file_name = 'test.csv';
        $import->file_path = 'test/path.csv';
        $import->importer = UserImporter::class;
        $import->total_rows = 10;
        $import->save();

        return new UserImporter($import, [], []);
    }

    #[Test]
    public function it_returns_correct_number_of_columns(): void
    {
        $columns = UserImporter::getColumns();
        $this->assertIsArray($columns);
        $this->assertCount(8, $columns);
    }

    #[Test]
    public function it_has_required_columns(): void
    {
        $columns = UserImporter::getColumns();
        $columnNames = array_map(fn($column) => $column->getName(), $columns);

        $expectedColumns = [
            'id', 'name', 'email', 'password', 'is_active',
            'is_super_admin', 'roles', 'permissions'
        ];

        foreach ($expectedColumns as $expectedColumn) {
            $this->assertContains($expectedColumn, $columnNames);
        }
    }

    #[Test]
    public function it_resolves_new_record_when_id_is_null(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => null, 'name' => 'Test User', 'email' => '<EMAIL>'];

        $record = $importer->resolveRecord();

        $this->assertInstanceOf(User::class, $record);
        $this->assertFalse($record->exists);
    }

    #[Test]
    public function it_resolves_existing_record_when_id_is_provided(): void
    {
        $user = User::factory()->create();
        $importer = $this->createMockImporter();
        $importer->data = ['id' => $user->id, 'name' => 'Updated Name'];

        $record = $importer->resolveRecord();

        $this->assertInstanceOf(User::class, $record);
        $this->assertTrue($record->exists);
        $this->assertEquals($user->id, $record->id);
    }

    #[Test]
    public function it_removes_empty_id_when_creating_new_user(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => '', 'name' => 'Test', 'email' => '<EMAIL>'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('id', $importer->data);
    }

    #[Test]
    public function it_removes_empty_password_when_updating(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => 1, 'password' => null, 'name' => 'Test', 'email' => '<EMAIL>'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('password', $importer->data);
    }

    #[Test]
    public function it_removes_empty_string_password(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['password' => '', 'name' => 'Test', 'email' => '<EMAIL>'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('password', $importer->data);
    }

    #[Test]
    public function it_preserves_valid_password(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['password' => 'validpassword123', 'name' => 'Test', 'email' => '<EMAIL>'];

        $importer->beforeFill();

        $this->assertEquals('validpassword123', $importer->data['password']);
    }

    #[Test]
    public function it_sets_default_values_for_boolean_fields(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['name' => 'Test', 'email' => '<EMAIL>'];

        $importer->beforeFill();

        $this->assertTrue($importer->data['is_active']);
        $this->assertFalse($importer->data['is_super_admin']);
    }

    #[Test]
    public function it_preserves_existing_boolean_values(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = [
            'name' => 'Test',
            'email' => '<EMAIL>',
            'is_active' => false,
            'is_super_admin' => true,
        ];

        $importer->beforeFill();

        $this->assertFalse($importer->data['is_active']);
        $this->assertTrue($importer->data['is_super_admin']);
    }

    #[Test]
    public function it_generates_correct_notification_body_with_failures(): void
    {
        // Create a real Import model for testing
        $import = new Import();
        $import->successful_rows = 10;
        $import->failed_rows = 2;

        $body = UserImporter::getCompletedNotificationBody($import);

        $this->assertEquals('Your user import has completed and 10 rows imported. 2 rows failed to import.', $body);
    }

    #[Test]
    public function it_generates_correct_notification_body_without_failures(): void
    {
        // Create a real Import model for testing
        $import = new Import();
        $import->successful_rows = 5;
        $import->failed_rows = 0;

        $body = UserImporter::getCompletedNotificationBody($import);

        $this->assertEquals('Your user import has completed and 5 rows imported.', $body);
    }

    #[Test]
    public function it_validates_boolean_columns_configuration(): void
    {
        $columns = UserImporter::getColumns();
        $booleanColumns = ['is_active', 'is_super_admin'];

        foreach ($booleanColumns as $columnName) {
            $column = collect($columns)->first(fn($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(ImportColumn::class, $column);
        }
    }

    #[Test]
    public function it_validates_relationship_columns_configuration(): void
    {
        $columns = UserImporter::getColumns();
        $relationshipColumns = ['roles', 'permissions'];

        foreach ($relationshipColumns as $columnName) {
            $column = collect($columns)->first(fn($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(\App\Filament\Actions\Imports\SyncRelationshipImportColumn::class, $column);
        }
    }

    #[Test]
    public function it_handles_null_id_in_data(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => null, 'name' => 'Test', 'email' => '<EMAIL>'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('id', $importer->data);
    }

    #[Test]
    public function it_handles_zero_id_in_data(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => 0, 'name' => 'Test', 'email' => '<EMAIL>'];

        $importer->beforeFill();

        $this->assertArrayNotHasKey('id', $importer->data);
    }

    #[Test]
    public function it_preserves_valid_id_in_data(): void
    {
        $importer = $this->createMockImporter();
        $importer->data = ['id' => 123, 'name' => 'Test', 'email' => '<EMAIL>'];

        $importer->beforeFill();

        $this->assertEquals(123, $importer->data['id']);
    }

    #[Test]
    public function it_creates_roles_and_permissions_relationships(): void
    {
        // Create test roles and permissions
        $role = Role::create(['name' => 'test-role']);
        $permission = Permission::create(['name' => 'test-permission']);

        $columns = UserImporter::getColumns();

        // Test roles column
        $rolesColumn = collect($columns)->first(fn($column) => $column->getName() === 'roles');
        $this->assertInstanceOf(\App\Filament\Actions\Imports\SyncRelationshipImportColumn::class, $rolesColumn);

        // Test permissions column
        $permissionsColumn = collect($columns)->first(fn($column) => $column->getName() === 'permissions');
        $this->assertInstanceOf(\App\Filament\Actions\Imports\SyncRelationshipImportColumn::class, $permissionsColumn);
    }

    #[Test]
    public function it_handles_empty_password_variations(): void
    {
        $variations = [null, '', '   ', 0, false];

        foreach ($variations as $variation) {
            $importer = $this->createMockImporter();
            $importer->data = ['password' => $variation, 'name' => 'Test', 'email' => '<EMAIL>'];

            $importer->beforeFill();

            $this->assertArrayNotHasKey('password', $importer->data, "Failed for variation: " . var_export($variation, true));
        }
    }

    #[Test]
    public function it_preserves_non_empty_password(): void
    {
        $validPasswords = ['password123', 'P@ssw0rd!', '12345678'];

        foreach ($validPasswords as $password) {
            $importer = $this->createMockImporter();
            $importer->data = ['password' => $password, 'name' => 'Test', 'email' => '<EMAIL>'];

            $importer->beforeFill();

            $this->assertEquals($password, $importer->data['password'], "Failed for password: $password");
        }
    }
}
