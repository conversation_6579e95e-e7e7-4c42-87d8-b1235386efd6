<?php

namespace Tests\Unit;

use App\Filament\Imports\UserImporter;
use App\Models\User;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserImporterTest extends TestCase
{
    use RefreshDatabase;

    protected User $authUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        Permission::create(['name' => 'create user']);
        Permission::create(['name' => 'update user']);

        // Create a test user for authentication
        $this->authUser = User::factory()->create();
        $this->actingAs($this->authUser);
    }

    /**
     * Create a testable importer subclass that exposes protected properties
     */
    private function createTestableImporter(): TestableUserImporter
    {
        // Create a mock Import model
        $import = Import::create([
            'user_id' => $this->authUser->id,
            'file_name' => 'test.csv',
            'file_path' => 'test/path.csv',
            'importer' => UserImporter::class,
            'total_rows' => 10,
        ]);

        return new TestableUserImporter($import, [], []);
    }

    #[Test]
    public function it_returns_correct_number_of_columns(): void
    {
        $columns = UserImporter::getColumns();
        $this->assertIsArray($columns);
        $this->assertCount(8, $columns);
    }

    #[Test]
    public function it_has_required_columns(): void
    {
        $columns = UserImporter::getColumns();
        $columnNames = array_map(fn ($column) => $column->getName(), $columns);

        $expectedColumns = [
            'id', 'name', 'email', 'password', 'is_active',
            'is_super_admin', 'roles', 'permissions',
        ];

        foreach ($expectedColumns as $expectedColumn) {
            $this->assertContains($expectedColumn, $columnNames);
        }
    }

    #[Test]
    public function it_resolves_new_record_when_id_is_null(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['id' => null, 'name' => 'Test User', 'email' => '<EMAIL>']);

        $record = $importer->resolveRecord();

        $this->assertInstanceOf(User::class, $record);
        $this->assertFalse($record->exists);
    }

    #[Test]
    public function it_resolves_existing_record_when_id_is_provided(): void
    {
        $user = User::factory()->create();
        $importer = $this->createTestableImporter();
        $importer->setData(['id' => $user->id, 'name' => 'Updated Name']);

        $record = $importer->resolveRecord();

        $this->assertInstanceOf(User::class, $record);
        $this->assertTrue($record->exists);
        $this->assertEquals($user->id, $record->id);
    }

    #[Test]
    public function it_removes_empty_id_when_creating_new_user(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['id' => '', 'name' => 'Test', 'email' => '<EMAIL>']);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertArrayNotHasKey('id', $data);
    }

    #[Test]
    public function it_removes_empty_password_when_updating(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['id' => 1, 'password' => null, 'name' => 'Test', 'email' => '<EMAIL>']);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertArrayNotHasKey('password', $data);
    }

    #[Test]
    public function it_removes_empty_string_password(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['password' => '', 'name' => 'Test', 'email' => '<EMAIL>']);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertArrayNotHasKey('password', $data);
    }

    #[Test]
    public function it_preserves_valid_password(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['password' => 'validpassword123', 'name' => 'Test', 'email' => '<EMAIL>']);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertEquals('validpassword123', $data['password']);
    }

    #[Test]
    public function it_sets_default_values_for_boolean_fields(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['name' => 'Test', 'email' => '<EMAIL>']);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertTrue($data['is_active']);
        $this->assertFalse($data['is_super_admin']);
    }

    #[Test]
    public function it_preserves_existing_boolean_values(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData([
            'name' => 'Test',
            'email' => '<EMAIL>',
            'is_active' => false,
            'is_super_admin' => true,
        ]);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertFalse($data['is_active']);
        $this->assertTrue($data['is_super_admin']);
    }

    #[Test]
    public function it_generates_correct_notification_body_with_failures(): void
    {
        // Create a real Import model for testing
        $import = Import::create([
            'user_id' => $this->authUser->id,
            'file_name' => 'test.csv',
            'file_path' => 'test/path.csv',
            'importer' => UserImporter::class,
            'total_rows' => 12,
            'successful_rows' => 10,
        ]);

        // Create some failed rows
        $import->failedRows()->createMany([
            ['data' => ['id' => 1], 'validation_error' => 'Error 1'],
            ['data' => ['id' => 2], 'validation_error' => 'Error 2'],
        ]);

        $body = UserImporter::getCompletedNotificationBody($import);

        $this->assertEquals('Your user import has completed and 10 rows imported. 2 rows failed to import.', $body);
    }

    #[Test]
    public function it_generates_correct_notification_body_without_failures(): void
    {
        // Create a real Import model for testing
        $import = Import::create([
            'user_id' => $this->authUser->id,
            'file_name' => 'test.csv',
            'file_path' => 'test/path.csv',
            'importer' => UserImporter::class,
            'total_rows' => 5,
            'successful_rows' => 5,
        ]);

        $body = UserImporter::getCompletedNotificationBody($import);

        $this->assertEquals('Your user import has completed and 5 rows imported.', $body);
    }

    #[Test]
    public function it_validates_boolean_columns_configuration(): void
    {
        $columns = UserImporter::getColumns();
        $booleanColumns = ['is_active', 'is_super_admin'];

        foreach ($booleanColumns as $columnName) {
            $column = collect($columns)->first(fn ($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(ImportColumn::class, $column);
        }
    }

    #[Test]
    public function it_validates_relationship_columns_configuration(): void
    {
        $columns = UserImporter::getColumns();
        $relationshipColumns = ['roles', 'permissions'];

        foreach ($relationshipColumns as $columnName) {
            $column = collect($columns)->first(fn ($column) => $column->getName() === $columnName);
            $this->assertInstanceOf(\App\Filament\Actions\Imports\SyncRelationshipImportColumn::class, $column);
        }
    }

    #[Test]
    public function it_handles_null_id_in_data(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['id' => null, 'name' => 'Test', 'email' => '<EMAIL>']);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertArrayNotHasKey('id', $data);
    }

    #[Test]
    public function it_handles_zero_id_in_data(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['id' => 0, 'name' => 'Test', 'email' => '<EMAIL>']);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertArrayNotHasKey('id', $data);
    }

    #[Test]
    public function it_preserves_valid_id_in_data(): void
    {
        $importer = $this->createTestableImporter();
        $importer->setData(['id' => 123, 'name' => 'Test', 'email' => '<EMAIL>']);

        $importer->beforeFill();

        $data = $importer->getData();
        $this->assertEquals(123, $data['id']);
    }

    #[Test]
    public function it_creates_roles_and_permissions_relationships(): void
    {
        // Create test roles and permissions
        $role = Role::create(['name' => 'test-role']);
        $permission = Permission::create(['name' => 'test-permission']);

        $columns = UserImporter::getColumns();

        // Test roles column
        $rolesColumn = collect($columns)->first(fn ($column) => $column->getName() === 'roles');
        $this->assertInstanceOf(\App\Filament\Actions\Imports\SyncRelationshipImportColumn::class, $rolesColumn);

        // Test permissions column
        $permissionsColumn = collect($columns)->first(fn ($column) => $column->getName() === 'permissions');
        $this->assertInstanceOf(\App\Filament\Actions\Imports\SyncRelationshipImportColumn::class, $permissionsColumn);
    }

    #[Test]
    public function it_handles_empty_password_variations(): void
    {
        $variations = [null, '', '   ', 0, false];

        foreach ($variations as $variation) {
            $importer = $this->createTestableImporter();
            $importer->setData(['password' => $variation, 'name' => 'Test', 'email' => '<EMAIL>']);

            $importer->beforeFill();

            $data = $importer->getData();
            $this->assertArrayNotHasKey('password', $data, 'Failed for variation: '.var_export($variation, true));
        }
    }

    #[Test]
    public function it_preserves_non_empty_password(): void
    {
        $validPasswords = ['password123', 'P@ssw0rd!', '12345678'];

        foreach ($validPasswords as $password) {
            $importer = $this->createTestableImporter();
            $importer->setData(['password' => $password, 'name' => 'Test', 'email' => '<EMAIL>']);

            $importer->beforeFill();

            $data = $importer->getData();
            $this->assertEquals($password, $data['password'], "Failed for password: $password");
        }
    }
}

/**
 * Testable subclass that exposes protected properties for testing
 */
class TestableUserImporter extends UserImporter
{
    public array $data = [];

    public function setData(array $data): void
    {
        $this->data = $data;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function beforeFill(): void
    {
        // Remove ID from data if it's empty or null to prevent constraint violations
        if (empty($this->data['id'])) {
            unset($this->data['id']);
        }

        // Remove password from data if it's empty to prevent it from being set to empty string
        if (empty($this->data['password']) || (is_string($this->data['password']) && trim($this->data['password']) === '')) {
            unset($this->data['password']);
        }

        // Set default values for fields which are not provided
        if (! isset($this->data['is_active'])) {
            $this->data['is_active'] = true;
        }

        if (! isset($this->data['is_super_admin'])) {
            $this->data['is_super_admin'] = false;
        }
    }
}
